2025-09-23 09:06:51,866 - __main__ - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250923_090651.log
2025-09-23 09:06:51,902 - main_fastapi - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250923_090651.log
2025-09-23 09:06:51,914 - main_fastapi - INFO - 日志系统初始化完成 - 日志文件: logs/battlefield_api_20250923_090651.log
2025-09-23 09:06:51,914 - main_fastapi - INFO - 战场态势分析API服务启动
2025-09-23 09:07:19,184 - main_fastapi - INFO - WebSocket连接建立，当前连接数: 1
2025-09-23 09:11:56,142 - main_fastapi - INFO - 收到战场分析请求 - 模拟时间: 60.0分钟
2025-09-23 09:11:56,142 - main_fastapi - INFO - ================================================================================
2025-09-23 09:11:56,142 - main_fastapi - INFO - 开始战场态势分析 - ID: ANALYSIS_1758589916142
2025-09-23 09:11:56,142 - main_fastapi - INFO - 模拟时间: 60.0分钟
2025-09-23 09:11:56,142 - main_fastapi - INFO - ================================================================================
2025-09-23 09:11:56,142 - main_fastapi - INFO - 接收到 3 个敌方单位, 2 个我方单位
2025-09-23 09:11:56,142 - main_fastapi - INFO - === 原始敌方单位信息 ===
2025-09-23 09:11:56,142 - main_fastapi - INFO - {
  "id": "ENEMY_001",
  "name": "T-90主战坦克",
  "type": "主战坦克",
  "side": "敌方",
  "position": {
    "longitude": 116.67548821767915,
    "latitude": 39.93871298615582,
    "altitude": 45.2
  },
  "status": {
    "health": 88.47001076489133,
    "ammo": 71.837309478271,
    "fuel": 63.94681355950477,
    "operational": true
  },
  "threat_level": "高",
  "confidence": 0.8587793159591852,
  "last_seen": "2025-09-23T09:11:56.140079",
  "speed": 25.0,
  "heading": 75.65087552180681
}
2025-09-23 09:11:56,142 - main_fastapi - INFO - {
  "id": "ENEMY_002",
  "name": "BTR-80装甲运兵车",
  "type": "装甲车",
  "side": "敌方",
  "position": {
    "longitude": 116.6924917498356,
    "latitude": 39.701845626595286,
    "altitude": 52.1
  },
  "status": {
    "health": 100.0,
    "ammo": 70.0,
    "fuel": 55.61482629343926,
    "operational": true
  },
  "threat_level": "中",
  "confidence": 0.6681653205301774,
  "last_seen": "2025-09-23T09:11:56.140133",
  "speed": 35.0,
  "heading": 148.80066202190605
}
2025-09-23 09:11:56,142 - main_fastapi - INFO - {
  "id": "ENEMY_003",
  "name": "苏-35战斗机",
  "type": "战斗机",
  "side": "敌方",
  "position": {
    "longitude": 108.94855190901478,
    "latitude": 35.92654238342234,
    "altitude": 8662.115665424182
  },
  "status": {
    "health": 100.0,
    "ammo": 82.23262626789995,
    "fuel": 50.80312550691887,
    "operational": true
  },
  "threat_level": "极高",
  "confidence": 0.727470021808991,
  "last_seen": "2025-09-23T09:11:56.140174",
  "speed": 800.0,
  "heading": 223.1840526566094
}
2025-09-23 09:11:56,142 - main_fastapi - INFO - === 原始我方单位信息 ===
2025-09-23 09:11:56,142 - main_fastapi - INFO - {
  "id": "FRIENDLY_001",
  "name": "99A主战坦克",
  "type": "主战坦克",
  "side": "我方",
  "position": {
    "longitude": 116.13451940487123,
    "latitude": 40.07993901070427,
    "altitude": 48.5
  },
  "status": {
    "health": 90.22348324113025,
    "ammo": 87.01909181833325,
    "fuel": 63.6334007536525,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-23T09:11:56.140209",
  "speed": 30.0,
  "heading": 291.4166801700668
}
2025-09-23 09:11:56,143 - main_fastapi - INFO - {
  "id": "FRIENDLY_002",
  "name": "歼-20战斗机",
  "type": "战斗机",
  "side": "我方",
  "position": {
    "longitude": 113.02250696425622,
    "latitude": 32.54835746431126,
    "altitude": 8923.742511924205
  },
  "status": {
    "health": 98.4906852619354,
    "ammo": 91.00320228189817,
    "fuel": 61.05224753157437,
    "operational": true
  },
  "threat_level": "",
  "confidence": 1.0,
  "last_seen": "2025-09-23T09:11:56.140245",
  "speed": 900.0,
  "heading": 177.3823840685501
}
2025-09-23 09:11:56,143 - main_fastapi - INFO - 阶段1: 查询武器知识库
2025-09-23 09:11:56,143 - main_fastapi - INFO - 开始查询武器知识库: T-90主战坦克 (主战坦克)
2025-09-23 09:11:56,504 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-23 09:11:56,504 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-23 09:11:56,507 - src.battlefield_data_service - INFO - 武器知识库查询成功 - T-90主战坦克(主战坦克)
2025-09-23 09:11:56,507 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 363.98ms
2025-09-23 09:11:56,507 - main_fastapi - INFO - 查询结果状态: found
2025-09-23 09:11:56,512 - main_fastapi - INFO - 开始查询武器知识库: BTR-80装甲运兵车 (装甲车)
2025-09-23 09:11:56,845 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-23 09:11:56,846 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-23 09:11:56,848 - src.battlefield_data_service - INFO - 武器知识库查询成功 - BTR-80装甲运兵车(装甲车)
2025-09-23 09:11:56,848 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 336.54ms
2025-09-23 09:11:56,848 - main_fastapi - INFO - 查询结果状态: found
2025-09-23 09:11:56,853 - main_fastapi - INFO - 开始查询武器知识库: 苏-35战斗机 (战斗机)
2025-09-23 09:11:57,183 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-23 09:11:57,183 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-23 09:11:57,185 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 苏-35战斗机(战斗机)
2025-09-23 09:11:57,186 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 332.82ms
2025-09-23 09:11:57,186 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-23 09:11:57,191 - main_fastapi - INFO - 开始查询武器知识库: 99A主战坦克 (主战坦克)
2025-09-23 09:11:57,523 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-23 09:11:57,524 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-23 09:11:57,526 - src.battlefield_data_service - WARNING - 武器知识库中未找到 - 99A主战坦克(主战坦克)
2025-09-23 09:11:57,526 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 335.34ms
2025-09-23 09:11:57,526 - main_fastapi - INFO - 查询结果状态: not_found
2025-09-23 09:11:57,531 - main_fastapi - INFO - 开始查询武器知识库: 歼-20战斗机 (战斗机)
2025-09-23 09:11:57,872 - src.database_connection - INFO - 数据库连接池初始化成功
2025-09-23 09:11:57,872 - src.battlefield_data_service - INFO - 战场数据流式处理服务初始化完成
2025-09-23 09:11:57,874 - src.battlefield_data_service - INFO - 武器知识库查询成功 - 歼-20战斗机(战斗机)
2025-09-23 09:11:57,874 - main_fastapi - INFO - 武器知识库查询完成 - 耗时: 343.4ms
2025-09-23 09:11:57,875 - main_fastapi - INFO - 查询结果状态: found
2025-09-23 09:11:57,881 - main_fastapi - INFO - 阶段2: 构建综合提示词
2025-09-23 09:11:57,881 - main_fastapi - INFO - 开始构建综合战场分析提示词
2025-09-23 09:11:57,881 - src.llm_prompt_builder - INFO - LLM提示词构建器初始化完成
2025-09-23 09:11:57,881 - src.llm_prompt_builder - INFO - 构建综合战场分析提示词
2025-09-23 09:11:57,882 - src.llm_prompt_builder - INFO - 综合战场分析提示词构建完成
2025-09-23 09:11:57,882 - main_fastapi - INFO - 综合提示词构建完成 - 耗时: 0.83ms
2025-09-23 09:11:57,882 - main_fastapi - INFO - 系统提示词长度: 187 字符
2025-09-23 09:11:57,882 - main_fastapi - INFO - 用户提示词长度: 2080 字符
2025-09-23 09:11:57,882 - main_fastapi - INFO - 阶段3: 调用大模型分析
2025-09-23 09:11:57,882 - main_fastapi - INFO - 开始调用大模型进行战场分析
2025-09-23 09:11:57,919 - src.llm_service - INFO - LLM服务初始化完成 - 提供商: openai_compatible
2025-09-23 09:11:57,919 - main_fastapi - INFO - LLM服务配置:
2025-09-23 09:11:57,919 - main_fastapi - INFO -   提供商: openai_compatible
2025-09-23 09:11:57,919 - main_fastapi - INFO -   模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-23 09:11:57,919 - main_fastapi - INFO -   超时设置: 60s
2025-09-23 09:11:57,919 - main_fastapi - INFO - === 系统提示词 ===
2025-09-23 09:11:57,919 - main_fastapi - INFO - 你是一名资深的军事战术分析专家，具有丰富的现代战争经验和深厚的军事理论基础。

你的任务是分析当前战场态势，评估敌我双方力量对比，并提供专业的战术建议。

分析要求：
1. 客观评估敌我双方的优势和劣势
2. 识别关键威胁和机会
3. 提供具体可行的战术建议
4. 考虑地形、天气等环境因素
5. 评估各种作战方案的风险和收益

请用中文回答，并以JSON格式输出分析结果。
2025-09-23 09:11:57,919 - main_fastapi - INFO - === 用户提示词 ===
2025-09-23 09:11:57,919 - main_fastapi - INFO - 请分析以下战场态势：

## 战场环境
- 地形: 平原地形
- 天气: 晴朗
- 能见度: 良好
- 时间: 白天

## 敌方单位 (3个)

### 敌方单位 1: T-90主战坦克
- 类型: 主战坦克
- 位置: 经度116.67548821767915, 纬度39.93871298615582, 高度45.2m
- 威胁等级: 高
- 置信度: 0.8587793159591852

- 性能数据:
  * 火力评级: 9/10
  * 防护评级: 9/10
  * 机动评级: 7/10
  * 主要武器: 125mm滑膛炮
  * 最大速度: 60.0km/h
  * 装甲厚度: 850.0mm
  * 优势: 强大的火力，优秀的防护，先进的火控系统
  * 弱点: 机动性相对较低，燃料消耗大，维护复杂
  * 推荐战术: 集群作战，火力压制，装甲突击
  * 反制战术: 空中打击，侧翼包围，反坦克导弹伏击

### 敌方单位 2: BTR-80装甲运兵车
- 类型: 装甲车
- 位置: 经度116.6924917498356, 纬度39.701845626595286, 高度52.1m
- 威胁等级: 中
- 置信度: 0.6681653205301774

- 性能数据:
  * 火力评级: 5/10
  * 防护评级: 6/10
  * 机动评级: 9/10
  * 主要武器: 14.5mm机枪
  * 最大速度: 80.0km/h
  * 装甲厚度: 300.0mm
  * 优势: 高机动性，两栖能力，载员能力强
  * 弱点: 装甲较薄，火力有限，易受重武器攻击
  * 推荐战术: 快速机动，步兵输送，火力支援
  * 反制战术: 重武器打击，地雷阻击，狙击手攻击

### 敌方单位 3: 苏-35战斗机
- 类型: 战斗机
- 位置: 经度108.94855190901478, 纬度35.92654238342234, 高度8662.115665424182m
- 威胁等级: 极高
- 置信度: 0.727470021808991


## 我方单位 (2个)

### 我方单位 1: 99A主战坦克
- 类型: 主战坦克
- 位置: 经度116.13451940487123, 纬度40.07993901070427, 高度48.5m
- 状态: 可作战
- 健康度: 90.22348324113025%
- 弹药状态: 87.01909181833325%
- 燃料状态: 63.6334007536525%

### 我方单位 2: 歼-20战斗机
- 类型: 战斗机
- 位置: 经度113.02250696425622, 纬度32.54835746431126, 高度8923.742511924205m
- 状态: 可作战
- 健康度: 98.4906852619354%
- 弹药状态: 91.00320228189817%
- 燃料状态: 61.05224753157437%

- 性能数据:
  * 火力评级: 10/10
  * 防护评级: 8/10
  * 机动评级: 10/10
  * 主要武器: 航炮+导弹
  * 最大速度: 2100.0km/h
  * 装甲厚度: 0.0mm
  * 优势: 隐身能力强，超音速巡航，先进航电
  * 弱点: 维护成本高，对基础设施要求高
  * 推荐战术: 超视距攻击，隐身突防，制空作战
  * 反制战术: 防空网拦截，电子对抗，多层防御


## 分析要求
请提供以下分析，并以JSON格式输出：

```json
{
  "威胁评估": {
    "整体威胁等级": "低/中/高/极高",
    "主要威胁": "描述最大的威胁",
    "威胁排序": ["威胁1", "威胁2", "威胁3"]
  },
  "力量对比": {
    "敌方优势": ["优势1", "优势2"],
    "我方优势": ["优势1", "优势2"],
    "关键弱点": ["弱点1", "弱点2"]
  },
  "战术建议": {
    "推荐策略": "主要作战策略",
    "优先目标": "首要打击目标",
    "兵力部署": "具体部署建议",
    "注意事项": ["注意事项1", "注意事项2"]
  },
  "作战方案": [
    {
      "方案名称": "方案1",
      "执行步骤": ["步骤1", "步骤2", "步骤3"],
      "成功概率": "百分比",
      "风险评估": "风险描述"
    }
  ],
  "应急预案": {
    "撤退路线": "撤退建议",
    "支援需求": "需要的支援",
    "备用方案": "备选策略"
  }
}
```

请确保分析全面、客观、实用，考虑实际作战中的各种因素。
2025-09-23 09:11:57,920 - main_fastapi - INFO - === 提示词结束 ===
2025-09-23 09:11:57,920 - src.llm_service - INFO - 调用LLM分析 - 提供商: openai_compatible
2025-09-23 09:11:57,920 - src.llm_service - INFO - 发送API请求 - 尝试 1/5, 超时设置: 60s
2025-09-23 09:12:28,781 - src.llm_service - INFO - LLM分析完成 - 耗时: 30860.95ms, 消耗tokens: 2838
2025-09-23 09:12:28,781 - main_fastapi - INFO - 大模型分析成功 - 耗时: 30898.99ms
2025-09-23 09:12:28,781 - main_fastapi - INFO - 消耗tokens: 2838
2025-09-23 09:12:28,781 - main_fastapi - INFO - 使用模型: Qwen2.5-72B-Instruct-GPTQ-Int4
2025-09-23 09:12:28,781 - main_fastapi - INFO - === 大模型分析结果 ===
2025-09-23 09:12:28,781 - main_fastapi - INFO - ```json
{
  "威胁评估": {
    "整体威胁等级": "极高",
    "主要威胁": "苏-35战斗机（极高的空中威胁）与T-90主战坦克（强大的地面火力与防护）",
    "威胁排序": ["苏-35战斗机", "T-90主战坦克", "BTR-80装甲运兵车"]
  },
  "力量对比": {
    "敌方优势": [
      "拥有强大的空中与地面协同作战能力（苏-35与T-90配合）",
      "T-90主战坦克具备出色的火力与防护能力",
      "BTR-80装甲车的高机动性和两栖能力可支援步兵作战"
    ],
    "我方优势": [
      "歼-20战斗机具备隐身能力和超音速巡航性能，适合作为空中主导力量",
      "99A主战坦克火力强大，且具备现代化火控系统，能够与T-90抗衡",
      "平原地形有利于机械化部队的快速机动"
    ],
    "关键弱点": [
      "敌方苏-35战斗机的制空权对我方地面部队构成巨大威胁",
      "我方缺乏足够的防空力量来应对敌方空中威胁",
      "BTR-80装甲车可能用于支援步兵渗透，对我方后方构成潜在威胁"
    ]
  },
  "战术建议": {
    "推荐策略": "先夺制空权，再集中地面火力打击敌方坦克，同时注意防范敌方装甲车的渗透",
    "优先目标": "苏-35战斗机（优先摧毁敌方空中力量）",
    "兵力部署": "歼-20战斗机立即升空争夺制空权；99A主战坦克向敌方T-90方向机动，准备正面交锋；同时派遣侦察无人机监控BTR-80装甲车动向",
    "注意事项": [
      "确保歼-20战斗机在隐蔽状态下接近敌方战斗机，利用其隐身性能进行超视距攻击",
      "99A主战坦克在移动过程中需保持无线电静默，避免暴露位置",
      "密切监视BTR-80装甲车的动向，防止其绕至我方侧翼或后方"
    ]
  },
  "作战方案": [
    {
      "方案名称": "制空权争夺与地面火力打击",
      "执行步骤": [
        "第一步：歼-20战斗机迅速升空，利用隐身性能进入敌方防空网盲区，寻找并锁定苏-35战斗机",
        "第二步：在歼-20获得空中优势后，99A主战坦克向敌方T-90方向机动，利用地形掩护接近并开火",
        "第三步：一旦发现BTR-80装甲车有渗透迹象，立即调遣地面部队或无人机对其进行拦截"
      ],
      "成功概率": "70%",
      "风险评估": "歼-20如果未能迅速取得制空权，地面部队将面临较大压力；BTR-80装甲车的渗透可能威胁到我方后方，增加战斗复杂性"
    }
  ],
  "应急预案": {
    "撤退路线": "99A主战坦克沿预设路线撤回高地或有利地形，歼-20战斗机在必要时提供空中掩护",
    "支援需求": "请求增派防空导弹系统和反坦克导弹支援，加强地面防御能力",
    "备用方案": "如果歼-20无法有效压制苏-35，考虑使用电子干扰设备破坏敌方空中指挥链，为地面部队争取时间"
  }
}
``` 

### 解析说明：
1. **威胁评估**：
   - 敌方的主要威胁是苏-35战斗机，其制空权能力对地面部队构成极大威胁。
   - T-90主战坦克的火力和防护能力较强，是地面的主要威胁。
   - BTR-80装甲车虽然威胁较小，但其高机动性和渗透能力不可忽视。

2. **力量对比**：
   - 敌方优势在于空中与地面的协同作战能力，尤其是苏-35战斗机的制空权。
   - 我方优势在于歼-20的隐身和超音速性能，以及99A主战坦克的强大火力和防护。
   - 关键弱点是我方缺乏足够防空力量保护地面部队，且BTR-80可能渗透造成威胁。

3. **战术建议**：
   - 优先制空权争夺，歼-20必须迅速压制苏-35，为地面部队创造安全环境。
   - 地面部队集中火力对付T-90，同时严密监控BTR-80的动向。

4. **作战方案**：
   - 制定详细的作战步骤，强调歼-20的隐身和超音速特性，同时99A主战坦克采取谨慎进攻策略。

5. **应急预案**：
   - 如果制空权争夺失败，需迅速调整战术，利用地形掩护撤退，并请求额外支援。同时，加强对BTR-80的监控和拦截措施。

此方案综合考虑了敌我双方的实力对比、地形条件和天气状况，旨在最大化我方优势并降低风险。
2025-09-23 09:12:28,781 - main_fastapi - INFO - === 分析结果结束 ===
2025-09-23 09:12:28,781 - main_fastapi - INFO - LLM分析结果解析成功
2025-09-23 09:12:28,781 - main_fastapi - INFO - ================================================================================
2025-09-23 09:12:28,781 - main_fastapi - INFO - 战场态势分析完 - ID: ANALYSIS_1758589916142
2025-09-23 09:12:28,781 - main_fastapi - INFO - 总处理时间: 32639.44ms
2025-09-23 09:12:28,781 - main_fastapi - INFO - LLM分析状态: success
2025-09-23 09:12:28,781 - main_fastapi - INFO - 消耗tokens: 2838
2025-09-23 09:12:28,781 - main_fastapi - INFO - ================================================================================
2025-09-23 09:12:28,782 - main_fastapi - INFO - 消息已广播到 1 个客户端
2025-09-23 09:12:28,782 - main_fastapi - INFO - 分析结果已通过WebSocket推送 - ID: ANALYSIS_1758589916142
2025-09-23 09:12:28,783 - main_fastapi - INFO - 分析报告已保存: reports/analysis_report_ANALYSIS_1758589916142_20250923_091228.json
2025-09-23 09:12:28,784 - main_fastapi - INFO - WebSocket连接断开，当前连接数: 0
2025-09-23 09:12:28,784 - main_fastapi - INFO - WebSocket客户端主动断开连接
2025-09-23 09:12:33,792 - main_fastapi - INFO - WebSocket连接建立，当前连接数: 1
2025-09-23 09:12:47,050 - main_fastapi - INFO - WebSocket连接断开，当前连接数: 0
2025-09-23 09:12:47,050 - main_fastapi - INFO - WebSocket客户端主动断开连接
2025-09-23 09:12:50,710 - main_fastapi - INFO - 战场态势分析API服务关闭
